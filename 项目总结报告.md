# 高危妊娠预测模型项目总结报告

## 📋 项目概述

本项目基于您提供的产检数据，成功构建了一个完整的高危妊娠风险预测系统。该系统能够根据产检指标自动评估妊娠风险等级，为临床决策提供科学依据。

## 📊 数据概况

### 数据规模
- **总记录数**: 312,618 条产检记录
- **时间跨度**: 2024年1月至2025年5月
- **原始特征数**: 63 个
- **最终使用特征数**: 24 个

### 目标变量分布
- **低风险**: 55,533 例 (17.8%)
- **一般风险**: 160,986 例 (51.5%)
- **高风险**: 96,099 例 (30.7%)

## 🔧 技术实现

### 1. 数据预处理
- ✅ 删除25个完全缺失的特征
- ✅ 删除10个高缺失率特征(>70%)
- ✅ 处理异常值和缺失值
- ✅ 数据类型转换和标准化

### 2. 特征工程
- ✅ 创建BMI分类特征
- ✅ 创建年龄分组特征
- ✅ 创建血压分类特征
- ✅ 对分类变量进行标签编码

### 3. 模型开发
训练了4种机器学习模型：

| 模型 | 准确率 | AUC | 特点 |
|------|--------|-----|------|
| **随机森林** | **93.76%** | **0.9856** | **最佳模型** |
| XGBoost | 75.62% | 0.8973 | 梯度提升 |
| 梯度提升 | 64.93% | 0.7981 | 集成学习 |
| 逻辑回归 | 57.32% | 0.7076 | 线性模型 |

### 4. 模型性能
**最佳模型（随机森林）详细性能：**

| 风险等级 | 精确率 | 召回率 | F1分数 | 样本数 |
|----------|--------|--------|--------|--------|
| 低风险 | 91% | 83% | 87% | 11,107 |
| 一般风险 | 93% | 96% | 94% | 32,197 |
| 高风险 | 96% | 97% | 96% | 19,220 |
| **总体** | **94%** | **94%** | **94%** | **62,524** |

## 🎯 关键特征重要性

根据随机森林模型分析，最重要的5个预测特征：

1. **体重** (19.48%) - 体重异常是重要风险指标
2. **年龄** (12.70%) - 高龄妊娠风险显著增加
3. **收缩压** (9.82%) - 血压异常提示妊娠高血压
4. **产检孕周** (9.61%) - 不同孕期风险特征不同
5. **舒张压** (9.39%) - 血压监测的重要组成

## 📁 项目交付物

### 核心文件
1. **`pregnancy_risk_prediction.py`** - 完整的机器学习流程
2. **`pregnancy_risk_predictor_app.py`** - 预测应用和演示
3. **`web_app.py`** - Streamlit Web界面
4. **`最佳妊娠风险预测模型.pkl`** - 训练好的模型文件
5. **`特征标准化器.pkl`** - 特征标准化器

### 分析结果
1. **`数据探索分析图表.png`** - 数据可视化图表
2. **`特征重要性分析.png`** - 特征重要性图表
3. **`项目总结报告.md`** - 本报告

## 🚀 使用方法

### 1. 命令行预测
```bash
python pregnancy_risk_predictor_app.py
```

### 2. Web界面
```bash
pip install streamlit plotly
streamlit run web_app.py
```

### 3. API调用示例
```python
from pregnancy_risk_predictor_app import PregnancyRiskPredictor

# 初始化预测器
predictor = PregnancyRiskPredictor()

# 患者数据
patient_data = {
    '产检孕周': 32, '年龄': 28, '孕次': 1, '产次': 0,
    'BMI': 22.5, '收缩压': 120, '舒张压': 80,
    # ... 其他特征
}

# 预测
result = predictor.predict_single(patient_data)
print(f"风险等级: {result['risk_label']}")
print(f"置信度: {result['confidence']:.2%}")
```

## 💡 临床应用建议

### 风险等级处理
- **🟢 低风险**: 继续定期产检，保持健康生活方式
- **🟡 一般风险**: 增加产检频率，密切监测相关指标
- **🔴 高风险**: 立即就医，接受专业评估和治疗

### 重点监测指标
1. **体重管理** - 控制孕期体重增长
2. **血压监测** - 定期测量血压变化
3. **年龄因素** - 高龄孕妇加强监护
4. **孕周管理** - 不同孕期采用相应策略

## 🔮 未来改进方向

### 1. 数据增强
- 收集更多生化指标数据
- 增加超声检查数据
- 补充遗传学信息

### 2. 模型优化
- 尝试深度学习模型
- 集成更多算法
- 增加时序分析

### 3. 功能扩展
- 添加风险因素解释
- 集成医学知识图谱
- 开发移动端应用

## 📈 项目价值

### 医疗价值
- **提高诊断效率**: 自动化风险评估，减少人工判断时间
- **降低医疗风险**: 早期识别高危患者，及时干预
- **优化资源配置**: 合理分配医疗资源，重点关注高危患者

### 技术价值
- **高准确率**: 93.76%的准确率达到临床应用标准
- **可解释性**: 提供特征重要性分析，便于医生理解
- **易于部署**: 提供多种使用方式，适应不同场景

## 🎉 项目总结

本项目成功构建了一个完整的高危妊娠预测系统，具有以下特点：

✅ **数据驱动**: 基于30万+真实产检数据训练
✅ **性能优异**: 准确率达93.76%，AUC达0.9856
✅ **实用性强**: 提供Web界面和API接口
✅ **可解释性**: 分析特征重要性，便于临床理解
✅ **易于部署**: 模型已保存，可直接投入使用

该系统可以有效辅助医生进行妊娠风险评估，提高诊断效率，降低医疗风险，具有重要的临床应用价值。

---

**项目完成时间**: 2025年6月28日  
**开发者**: AI Assistant  
**技术栈**: Python, Scikit-learn, XGBoost, Streamlit, Plotly
