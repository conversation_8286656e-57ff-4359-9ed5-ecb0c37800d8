#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高危妊娠预测模型项目
High-Risk Pregnancy Prediction Model

作者: AI Assistant
日期: 2025-06-28
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.impute import SimpleImputer
import xgboost as xgb
import shap
from datetime import datetime

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

class PregnancyRiskPredictor:
    """高危妊娠预测模型类"""
    
    def __init__(self, data_path):
        """
        初始化预测器
        
        Args:
            data_path (str): 数据文件路径
        """
        self.data_path = data_path
        self.raw_data = None
        self.processed_data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.best_model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        try:
            self.raw_data = pd.read_csv(self.data_path, encoding='utf-8')
            print(f"数据加载成功！数据形状: {self.raw_data.shape}")
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            try:
                self.raw_data = pd.read_csv(self.data_path, encoding='gbk')
                print(f"使用GBK编码加载成功！数据形状: {self.raw_data.shape}")
                return True
            except Exception as e2:
                print(f"GBK编码也失败: {e2}")
                return False
    
    def explore_data(self):
        """数据探索分析"""
        if self.raw_data is None:
            print("请先加载数据！")
            return
            
        print("\n" + "="*50)
        print("数据探索分析")
        print("="*50)
        
        # 基本信息
        print(f"\n1. 数据基本信息:")
        print(f"   - 数据形状: {self.raw_data.shape}")
        print(f"   - 列数: {len(self.raw_data.columns)}")
        print(f"   - 行数: {len(self.raw_data)}")
        
        # 列名信息
        print(f"\n2. 列名信息:")
        for i, col in enumerate(self.raw_data.columns, 1):
            print(f"   {i:2d}. {col}")
        
        # 数据类型
        print(f"\n3. 数据类型分布:")
        dtype_counts = self.raw_data.dtypes.value_counts()
        for dtype, count in dtype_counts.items():
            print(f"   {dtype}: {count} 列")
        
        # 缺失值分析
        print(f"\n4. 缺失值分析:")
        missing_data = self.raw_data.isnull().sum()
        missing_percent = (missing_data / len(self.raw_data)) * 100
        missing_df = pd.DataFrame({
            '缺失数量': missing_data,
            '缺失百分比': missing_percent
        }).sort_values('缺失百分比', ascending=False)
        
        print("   缺失值最多的前10个特征:")
        print(missing_df.head(10))
        
        # 目标变量分析
        if '风险等级' in self.raw_data.columns:
            print(f"\n5. 风险等级分布:")
            risk_counts = self.raw_data['风险等级'].value_counts()
            print(risk_counts)
            print(f"\n   风险等级百分比:")
            print((risk_counts / len(self.raw_data) * 100).round(2))
        
        # 数值型特征统计
        numeric_cols = self.raw_data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"\n6. 数值型特征统计摘要:")
            print(self.raw_data[numeric_cols].describe())
        
        return missing_df
    
    def visualize_data(self):
        """数据可视化"""
        if self.raw_data is None:
            print("请先加载数据！")
            return
            
        print("\n正在生成数据可视化图表...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('妊娠数据探索性分析', fontsize=16, fontweight='bold')
        
        # 1. 风险等级分布
        if '风险等级' in self.raw_data.columns:
            risk_counts = self.raw_data['风险等级'].value_counts()
            axes[0, 0].pie(risk_counts.values, labels=risk_counts.index, autopct='%1.1f%%')
            axes[0, 0].set_title('风险等级分布')
        
        # 2. 年龄分布
        if '年龄' in self.raw_data.columns:
            self.raw_data['年龄'].hist(bins=20, ax=axes[0, 1], alpha=0.7)
            axes[0, 1].set_title('年龄分布')
            axes[0, 1].set_xlabel('年龄')
            axes[0, 1].set_ylabel('频数')
        
        # 3. BMI分布
        if 'BMI' in self.raw_data.columns:
            bmi_data = self.raw_data['BMI'].dropna()
            if len(bmi_data) > 0:
                bmi_data.hist(bins=20, ax=axes[1, 0], alpha=0.7)
                axes[1, 0].set_title('BMI分布')
                axes[1, 0].set_xlabel('BMI')
                axes[1, 0].set_ylabel('频数')
        
        # 4. 缺失值热图
        missing_data = self.raw_data.isnull().sum().sort_values(ascending=False)
        top_missing = missing_data.head(20)
        if len(top_missing) > 0:
            axes[1, 1].barh(range(len(top_missing)), top_missing.values)
            axes[1, 1].set_yticks(range(len(top_missing)))
            axes[1, 1].set_yticklabels(top_missing.index, fontsize=8)
            axes[1, 1].set_title('缺失值最多的20个特征')
            axes[1, 1].set_xlabel('缺失数量')
        
        plt.tight_layout()
        plt.savefig('数据探索分析图表.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("图表已保存为 '数据探索分析图表.png'")

    def preprocess_data(self):
        """数据预处理"""
        if self.raw_data is None:
            print("请先加载数据！")
            return False

        print("\n" + "="*50)
        print("数据预处理")
        print("="*50)

        # 复制原始数据
        self.processed_data = self.raw_data.copy()

        # 1. 删除完全缺失的列
        print("\n1. 处理完全缺失的特征...")
        missing_100_percent = []
        for col in self.processed_data.columns:
            missing_rate = self.processed_data[col].isnull().sum() / len(self.processed_data)
            if missing_rate == 1.0:
                missing_100_percent.append(col)

        print(f"   发现 {len(missing_100_percent)} 个完全缺失的特征:")
        for col in missing_100_percent[:10]:  # 只显示前10个
            print(f"   - {col}")
        if len(missing_100_percent) > 10:
            print(f"   ... 还有 {len(missing_100_percent) - 10} 个")

        # 删除完全缺失的列
        self.processed_data = self.processed_data.drop(columns=missing_100_percent)
        print(f"   删除后数据形状: {self.processed_data.shape}")

        # 2. 处理目标变量
        print("\n2. 处理目标变量...")
        if '风险等级' in self.processed_data.columns:
            # 简化风险等级分类
            risk_mapping = {
                '低风险': 0,
                '低风险+传染病': 0,
                '一般风险': 1,
                '一般风险+传染病': 1,
                '较高风险': 2,
                '较高风险+传染病': 2,
                '高风险': 2
            }

            self.processed_data['风险等级_编码'] = self.processed_data['风险等级'].map(risk_mapping)
            print("   风险等级重新编码:")
            print("   - 0: 低风险")
            print("   - 1: 一般风险")
            print("   - 2: 高风险")

            # 检查编码后的分布
            risk_dist = self.processed_data['风险等级_编码'].value_counts().sort_index()
            print(f"\n   编码后分布:")
            for risk_level, count in risk_dist.items():
                percentage = count / len(self.processed_data) * 100
                print(f"   - 等级 {risk_level}: {count:,} 例 ({percentage:.1f}%)")

        # 3. 处理高缺失率特征
        print("\n3. 处理高缺失率特征...")
        high_missing_threshold = 0.7  # 缺失率超过70%的特征
        high_missing_cols = []

        for col in self.processed_data.columns:
            if col not in ['风险等级', '风险等级_编码']:
                missing_rate = self.processed_data[col].isnull().sum() / len(self.processed_data)
                if missing_rate > high_missing_threshold:
                    high_missing_cols.append((col, missing_rate))

        high_missing_cols.sort(key=lambda x: x[1], reverse=True)
        print(f"   发现 {len(high_missing_cols)} 个高缺失率特征 (>{high_missing_threshold*100}%):")
        for col, rate in high_missing_cols[:10]:
            print(f"   - {col}: {rate*100:.1f}%")

        # 删除高缺失率特征
        cols_to_drop = [col for col, rate in high_missing_cols]
        self.processed_data = self.processed_data.drop(columns=cols_to_drop)
        print(f"   删除高缺失率特征后数据形状: {self.processed_data.shape}")

        # 4. 处理数据类型
        print("\n4. 处理数据类型...")

        # 识别数值型特征
        numeric_cols = []
        categorical_cols = []

        for col in self.processed_data.columns:
            if col in ['保健号_科学计数法', '保健号', '产检日期', '风险评估日期', '风险等级', '风险颜色']:
                continue  # 跳过ID和日期列

            if self.processed_data[col].dtype in ['int64', 'float64']:
                numeric_cols.append(col)
            else:
                categorical_cols.append(col)

        print(f"   数值型特征: {len(numeric_cols)} 个")
        print(f"   分类型特征: {len(categorical_cols)} 个")

        # 5. 处理异常值
        print("\n5. 处理异常值...")
        outlier_counts = {}

        for col in numeric_cols:
            if col == '风险等级_编码':
                continue

            data = self.processed_data[col].dropna()
            if len(data) > 0:
                Q1 = data.quantile(0.25)
                Q3 = data.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = ((data < lower_bound) | (data > upper_bound)).sum()
                if outliers > 0:
                    outlier_counts[col] = outliers

        print(f"   发现异常值的特征数: {len(outlier_counts)}")
        if outlier_counts:
            sorted_outliers = sorted(outlier_counts.items(), key=lambda x: x[1], reverse=True)
            for col, count in sorted_outliers[:5]:
                print(f"   - {col}: {count} 个异常值")

        # 6. 处理缺失值
        print("\n6. 处理剩余缺失值...")
        remaining_missing = self.processed_data.isnull().sum()
        cols_with_missing = remaining_missing[remaining_missing > 0]

        if len(cols_with_missing) > 0:
            print(f"   还有 {len(cols_with_missing)} 个特征存在缺失值")

            # 对数值型特征用中位数填充
            for col in numeric_cols:
                if col in cols_with_missing.index:
                    median_val = self.processed_data[col].median()
                    self.processed_data[col].fillna(median_val, inplace=True)

            # 对分类型特征用众数填充
            for col in categorical_cols:
                if col in cols_with_missing.index:
                    mode_val = self.processed_data[col].mode()
                    if len(mode_val) > 0:
                        self.processed_data[col].fillna(mode_val[0], inplace=True)
                    else:
                        self.processed_data[col].fillna('未知', inplace=True)

            print("   缺失值填充完成")

        print(f"\n数据预处理完成！最终数据形状: {self.processed_data.shape}")
        return True

    def create_features(self):
        """特征工程"""
        if self.processed_data is None:
            print("请先进行数据预处理！")
            return False

        print("\n" + "="*50)
        print("特征工程")
        print("="*50)

        # 1. 创建新特征
        print("\n1. 创建新特征...")

        # BMI分类
        if 'BMI' in self.processed_data.columns:
            def categorize_bmi(bmi):
                if pd.isna(bmi) or bmi == 0:
                    return '未知'
                elif bmi < 18.5:
                    return '偏瘦'
                elif bmi < 24:
                    return '正常'
                elif bmi < 28:
                    return '超重'
                else:
                    return '肥胖'

            self.processed_data['BMI分类'] = self.processed_data['BMI'].apply(categorize_bmi)
            print("   - 创建BMI分类特征")

        # 年龄分组
        if '年龄' in self.processed_data.columns:
            def categorize_age(age):
                if pd.isna(age):
                    return '未知'
                elif age < 20:
                    return '青少年'
                elif age < 35:
                    return '适龄'
                else:
                    return '高龄'

            self.processed_data['年龄分组'] = self.processed_data['年龄'].apply(categorize_age)
            print("   - 创建年龄分组特征")

        # 血压分类
        if '收缩压' in self.processed_data.columns and '舒张压' in self.processed_data.columns:
            def categorize_bp(sbp, dbp):
                if pd.isna(sbp) or pd.isna(dbp):
                    return '未知'
                elif sbp >= 140 or dbp >= 90:
                    return '高血压'
                elif sbp >= 130 or dbp >= 80:
                    return '血压偏高'
                else:
                    return '正常'

            self.processed_data['血压分类'] = self.processed_data.apply(
                lambda row: categorize_bp(row['收缩压'], row['舒张压']), axis=1
            )
            print("   - 创建血压分类特征")

        # 2. 编码分类特征
        print("\n2. 编码分类特征...")
        categorical_features = []

        for col in self.processed_data.columns:
            if self.processed_data[col].dtype == 'object' and col not in [
                '保健号_科学计数法', '保健号', '产检日期', '风险评估日期',
                '风险等级', '风险颜色', '特殊检查类型', '医生建议', '风险评估', '风险因素'
            ]:
                categorical_features.append(col)

        print(f"   需要编码的分类特征: {len(categorical_features)} 个")

        # 使用标签编码
        for col in categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()

            # 处理可能的缺失值
            self.processed_data[col] = self.processed_data[col].astype(str)
            self.processed_data[f'{col}_编码'] = self.label_encoders[col].fit_transform(self.processed_data[col])

        print(f"   完成 {len(categorical_features)} 个特征的标签编码")

        print(f"\n特征工程完成！数据形状: {self.processed_data.shape}")
        return True

    def prepare_model_data(self):
        """准备模型训练数据"""
        if self.processed_data is None:
            print("请先进行特征工程！")
            return False

        print("\n" + "="*50)
        print("准备模型训练数据")
        print("="*50)

        # 选择特征列
        feature_cols = []
        exclude_cols = [
            '保健号_科学计数法', '保健号', '产检日期', '风险评估日期',
            '风险等级', '风险颜色', '特殊检查类型', '医生建议',
            '风险评估', '风险因素', '风险等级_编码'
        ]

        for col in self.processed_data.columns:
            if col not in exclude_cols:
                # 只选择数值型特征和编码后的分类特征
                if self.processed_data[col].dtype in ['int64', 'float64']:
                    feature_cols.append(col)
                elif col.endswith('_编码') and col != '风险等级_编码':
                    feature_cols.append(col)

        print(f"选择的特征数量: {len(feature_cols)}")

        # 准备特征矩阵和目标变量
        X = self.processed_data[feature_cols].copy()
        y = self.processed_data['风险等级_编码'].copy()

        # 处理可能的无穷大值
        X = X.replace([np.inf, -np.inf], np.nan)

        # 再次检查缺失值
        missing_cols = X.isnull().sum()
        if missing_cols.sum() > 0:
            print("发现缺失值，进行最终填充...")
            for col in X.columns:
                if X[col].isnull().sum() > 0:
                    if X[col].dtype in ['int64', 'float64']:
                        X[col].fillna(X[col].median(), inplace=True)
                    else:
                        X[col].fillna(X[col].mode()[0] if len(X[col].mode()) > 0 else 0, inplace=True)

        # 数据分割
        print("\n分割训练集和测试集...")
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        print(f"训练集大小: {self.X_train.shape}")
        print(f"测试集大小: {self.X_test.shape}")
        print(f"特征数量: {self.X_train.shape[1]}")

        # 特征标准化
        print("\n进行特征标准化...")
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)

        print("数据准备完成！")
        return True

    def train_models(self):
        """训练多种机器学习模型"""
        if self.X_train is None:
            print("请先准备模型数据！")
            return False

        print("\n" + "="*50)
        print("模型训练")
        print("="*50)

        # 定义模型
        models = {
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
            'Gradient Boosting': GradientBoostingClassifier(random_state=42),
            'XGBoost': xgb.XGBClassifier(random_state=42, eval_metric='mlogloss')
        }

        # 训练模型
        for name, model in models.items():
            print(f"\n训练 {name}...")

            try:
                if name == 'Logistic Regression':
                    # 逻辑回归使用标准化数据
                    model.fit(self.X_train_scaled, self.y_train)
                    train_score = model.score(self.X_train_scaled, self.y_train)
                    test_score = model.score(self.X_test_scaled, self.y_test)
                else:
                    # 树模型使用原始数据
                    model.fit(self.X_train, self.y_train)
                    train_score = model.score(self.X_train, self.y_train)
                    test_score = model.score(self.X_test, self.y_test)

                self.models[name] = model

                print(f"   训练集准确率: {train_score:.4f}")
                print(f"   测试集准确率: {test_score:.4f}")

            except Exception as e:
                print(f"   训练失败: {e}")

        print(f"\n成功训练 {len(self.models)} 个模型")
        return True

    def evaluate_models(self):
        """评估模型性能"""
        if not self.models:
            print("请先训练模型！")
            return False

        print("\n" + "="*50)
        print("模型评估")
        print("="*50)

        results = {}

        for name, model in self.models.items():
            print(f"\n评估 {name}:")

            try:
                # 预测
                if name == 'Logistic Regression':
                    y_pred = model.predict(self.X_test_scaled)
                    y_pred_proba = model.predict_proba(self.X_test_scaled)
                else:
                    y_pred = model.predict(self.X_test)
                    y_pred_proba = model.predict_proba(self.X_test)

                # 计算指标
                accuracy = (y_pred == self.y_test).mean()

                # 多分类ROC AUC
                try:
                    auc_score = roc_auc_score(self.y_test, y_pred_proba, multi_class='ovr')
                except:
                    auc_score = 0.0

                results[name] = {
                    'accuracy': accuracy,
                    'auc': auc_score,
                    'predictions': y_pred,
                    'probabilities': y_pred_proba
                }

                print(f"   准确率: {accuracy:.4f}")
                print(f"   AUC: {auc_score:.4f}")

                # 分类报告
                print(f"\n   分类报告:")
                report = classification_report(self.y_test, y_pred,
                                             target_names=['低风险', '一般风险', '高风险'])
                print(report)

            except Exception as e:
                print(f"   评估失败: {e}")

        # 找出最佳模型
        if results:
            best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
            self.best_model = self.models[best_model_name]

            print(f"\n最佳模型: {best_model_name}")
            print(f"最佳准确率: {results[best_model_name]['accuracy']:.4f}")

        return results

def main():
    """主函数"""
    print("高危妊娠预测模型项目启动")
    print("="*50)

    # 初始化预测器
    predictor = PregnancyRiskPredictor('合并后的妊娠数据.csv')

    # 加载数据
    if not predictor.load_data():
        print("数据加载失败，程序退出")
        return

    # 数据探索
    missing_analysis = predictor.explore_data()

    # 数据可视化
    predictor.visualize_data()

    print("\n第一阶段：数据探索完成！")

    # 数据预处理
    if not predictor.preprocess_data():
        print("数据预处理失败，程序退出")
        return

    # 特征工程
    if not predictor.create_features():
        print("特征工程失败，程序退出")
        return

    print("\n第二阶段：数据预处理和特征工程完成！")

    # 准备模型数据
    if not predictor.prepare_model_data():
        print("数据准备失败，程序退出")
        return

    # 训练模型
    if not predictor.train_models():
        print("模型训练失败，程序退出")
        return

    # 评估模型
    results = predictor.evaluate_models()

    if results:
        print("\n第三阶段：模型开发和评估完成！")
        print("="*50)
        print("项目总结:")
        print(f"- 处理了 {len(predictor.raw_data):,} 条产检记录")
        print(f"- 最终使用 {predictor.X_train.shape[1]} 个特征")
        print(f"- 训练了 {len(predictor.models)} 个机器学习模型")
        print(f"- 最佳模型准确率: {max([r['accuracy'] for r in results.values()]):.4f}")

        # 保存模型
        import joblib
        if predictor.best_model:
            joblib.dump(predictor.best_model, '最佳妊娠风险预测模型.pkl')
            joblib.dump(predictor.scaler, '特征标准化器.pkl')
            print("\n模型已保存:")
            print("- 最佳妊娠风险预测模型.pkl")
            print("- 特征标准化器.pkl")
    else:
        print("模型评估失败")

if __name__ == "__main__":
    main()
