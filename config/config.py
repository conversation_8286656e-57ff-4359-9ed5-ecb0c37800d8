"""
高危妊娠预测系统配置文件
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
MODELS_DIR = PROJECT_ROOT / "models"
LOGS_DIR = PROJECT_ROOT / "logs"

# 数据路径
RAW_DATA_DIR = DATA_DIR / "raw"
PROCESSED_DATA_DIR = DATA_DIR / "processed"
FEATURES_DATA_DIR = DATA_DIR / "features"
SPLITS_DATA_DIR = DATA_DIR / "splits"

# 模型配置
MODEL_CONFIG = {
    "random_state": 42,
    "test_size": 0.2,
    "validation_size": 0.2,
    "cross_validation_folds": 5,
    "target_column": "风险等级",
    "id_column": "保健号"
}

# 特征工程配置
FEATURE_CONFIG = {
    "categorical_features": [
        "民族", "就诊类型", "风险颜色", "分娩方式", "分娩并发症"
    ],
    "numerical_features": [
        "年龄", "孕次", "产次", "BMI", "体重", "收缩压", "舒张压",
        "产检孕周", "分娩孕周", "新生儿体重", "产后出血量"
    ],
    "binary_features": [
        "流产史", "既往GDM史", "既往病史", "家族史"
    ],
    "high_missing_threshold": 0.8,
    "low_variance_threshold": 0.01
}

# 模型参数
MODELS_CONFIG = {
    "logistic_regression": {
        "C": [0.1, 1.0, 10.0],
        "penalty": ["l1", "l2"],
        "solver": ["liblinear", "saga"],
        "max_iter": 1000
    },
    "random_forest": {
        "n_estimators": [100, 200, 300],
        "max_depth": [10, 20, None],
        "min_samples_split": [2, 5, 10],
        "min_samples_leaf": [1, 2, 4]
    },
    "xgboost": {
        "n_estimators": [100, 200, 300],
        "max_depth": [3, 6, 9],
        "learning_rate": [0.01, 0.1, 0.2],
        "subsample": [0.8, 0.9, 1.0]
    },
    "lightgbm": {
        "n_estimators": [100, 200, 300],
        "max_depth": [3, 6, 9],
        "learning_rate": [0.01, 0.1, 0.2],
        "num_leaves": [31, 50, 100]
    }
}

# 评估指标
EVALUATION_METRICS = [
    "accuracy", "precision", "recall", "f1", "roc_auc"
]

# 风险等级映射
RISK_LEVEL_MAPPING = {
    "低风险": 0,
    "一般风险": 1,
    "较高风险": 2,
    "高风险": 3
}

# API配置
API_CONFIG = {
    "host": "0.0.0.0",
    "port": 8000,
    "debug": True,
    "model_path": MODELS_DIR / "best_model.pkl",
    "preprocessor_path": MODELS_DIR / "preprocessor.pkl"
}

# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.StreamHandler",
        },
        "file": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.FileHandler",
            "filename": LOGS_DIR / "app.log",
            "mode": "a",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default", "file"],
            "level": "INFO",
            "propagate": False
        }
    }
}

# 创建必要的目录
for directory in [DATA_DIR, MODELS_DIR, LOGS_DIR, RAW_DATA_DIR, 
                  PROCESSED_DATA_DIR, FEATURES_DATA_DIR, SPLITS_DATA_DIR]:
    directory.mkdir(parents=True, exist_ok=True) 