# 数据处理和分析
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# 机器学习
scikit-learn>=1.1.0
xgboost>=1.6.0
lightgbm>=3.3.0
imbalanced-learn>=0.9.0

# 深度学习（可选）
tensorflow>=2.10.0
torch>=1.12.0

# 数据可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0

# 模型解释
shap>=0.41.0
lime>=0.2.0

# Web框架
streamlit>=1.12.0
flask>=2.2.0
fastapi>=0.85.0
uvicorn>=0.18.0

# 数据库
sqlalchemy>=1.4.0
pymongo>=4.2.0

# 工具包
joblib>=1.1.0
pickle5>=0.0.11
tqdm>=4.64.0
python-dotenv>=0.20.0

# 测试
pytest>=7.1.0
pytest-cov>=3.0.0

# 代码质量
black>=22.6.0
flake8>=5.0.0
isort>=5.10.0

# Jupyter
jupyter>=1.0.0
ipykernel>=6.15.0
ipywidgets>=7.7.0

# 部署
gunicorn>=20.1.0
docker>=6.0.0

# 医疗数据处理
pydicom>=2.3.0
nibabel>=4.0.0

# 统计分析
statsmodels>=0.13.0
pingouin>=0.5.0 