#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高危妊娠预测应用
Pregnancy Risk Prediction Application

作者: AI Assistant
日期: 2025-06-28
"""

import pandas as pd
import numpy as np
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PregnancyRiskPredictor:
    """高危妊娠风险预测器"""
    
    def __init__(self, model_path='最佳妊娠风险预测模型.pkl', scaler_path='特征标准化器.pkl'):
        """
        初始化预测器
        
        Args:
            model_path (str): 模型文件路径
            scaler_path (str): 标准化器文件路径
        """
        try:
            self.model = joblib.load(model_path)
            self.scaler = joblib.load(scaler_path)
            print("模型加载成功！")
        except Exception as e:
            print(f"模型加载失败: {e}")
            self.model = None
            self.scaler = None
        
        # 风险等级映射
        self.risk_labels = {0: '低风险', 1: '一般风险', 2: '高风险'}
        self.risk_colors = {0: 'green', 1: 'orange', 2: 'red'}
        
        # 特征名称（需要与训练时保持一致）
        self.feature_names = [
            '产检孕周', '年龄', '孕次', '产次', '流产史', '既往GDM史', 
            '既往病史', '家族史', 'BMI', '体重', '收缩压', '舒张压', 
            '血红蛋白', '白细胞计数', '血小板计数', '分娩孕周', '产后出血量',
            '就诊类型_编码', '民族_编码', 'BMI分类_编码', '年龄分组_编码', 
            '血压分类_编码', '尿蛋白_编码', '尿糖_编码'
        ]
    
    def predict_single(self, patient_data):
        """
        预测单个患者的风险等级
        
        Args:
            patient_data (dict): 患者数据字典
            
        Returns:
            dict: 预测结果
        """
        if self.model is None:
            return {"error": "模型未加载"}
        
        try:
            # 构建特征向量
            features = []
            for feature in self.feature_names:
                if feature in patient_data:
                    features.append(patient_data[feature])
                else:
                    features.append(0)  # 默认值
            
            # 转换为numpy数组
            X = np.array(features).reshape(1, -1)
            
            # 预测
            prediction = self.model.predict(X)[0]
            probabilities = self.model.predict_proba(X)[0]
            
            result = {
                'risk_level': prediction,
                'risk_label': self.risk_labels[prediction],
                'probabilities': {
                    '低风险': probabilities[0],
                    '一般风险': probabilities[1],
                    '高风险': probabilities[2]
                },
                'confidence': max(probabilities)
            }
            
            return result
            
        except Exception as e:
            return {"error": f"预测失败: {e}"}
    
    def get_feature_importance(self):
        """获取特征重要性"""
        if self.model is None or not hasattr(self.model, 'feature_importances_'):
            return None
        
        importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        return importance_df
    
    def plot_feature_importance(self, top_n=15):
        """绘制特征重要性图"""
        importance_df = self.get_feature_importance()
        if importance_df is None:
            print("无法获取特征重要性")
            return
        
        plt.figure(figsize=(10, 8))
        top_features = importance_df.head(top_n)
        
        sns.barplot(data=top_features, x='importance', y='feature', palette='viridis')
        plt.title(f'前{top_n}个最重要特征', fontsize=16, fontweight='bold')
        plt.xlabel('重要性分数', fontsize=12)
        plt.ylabel('特征名称', fontsize=12)
        plt.tight_layout()
        plt.savefig('特征重要性分析.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return top_features
    
    def create_risk_assessment_report(self, patient_data):
        """创建风险评估报告"""
        prediction = self.predict_single(patient_data)
        
        if "error" in prediction:
            return prediction
        
        report = f"""
        ==========================================
        高危妊娠风险评估报告
        ==========================================
        
        预测结果: {prediction['risk_label']}
        置信度: {prediction['confidence']:.2%}
        
        各风险等级概率:
        - 低风险: {prediction['probabilities']['低风险']:.2%}
        - 一般风险: {prediction['probabilities']['一般风险']:.2%}
        - 高风险: {prediction['probabilities']['高风险']:.2%}
        
        建议:
        """
        
        if prediction['risk_level'] == 0:
            report += """
        - 继续定期产检
        - 保持健康的生活方式
        - 注意营养均衡
        """
        elif prediction['risk_level'] == 1:
            report += """
        - 增加产检频率
        - 密切监测相关指标
        - 遵医嘱进行必要检查
        - 注意休息，避免过度劳累
        """
        else:
            report += """
        - 立即就医，接受专业评估
        - 严格按医嘱进行治疗
        - 可能需要住院观察
        - 家属应密切关注患者状况
        """
        
        report += f"""
        
        ==========================================
        报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
        ==========================================
        """
        
        return report

def demo_prediction():
    """演示预测功能"""
    print("高危妊娠风险预测系统演示")
    print("="*50)
    
    # 初始化预测器
    predictor = PregnancyRiskPredictor()
    
    if predictor.model is None:
        print("模型加载失败，无法进行演示")
        return
    
    # 演示数据
    demo_patients = [
        {
            "name": "患者A",
            "data": {
                '产检孕周': 32, '年龄': 25, '孕次': 2, '产次': 0, '流产史': 0,
                '既往GDM史': 0, '既往病史': 0, '家族史': 0, 'BMI': 22.5, '体重': 65.0,
                '收缩压': 118, '舒张压': 66, '血红蛋白': 120, '白细胞计数': 8.0,
                '血小板计数': 250, '分娩孕周': 0, '产后出血量': 0,
                '就诊类型_编码': 0, '民族_编码': 0, 'BMI分类_编码': 1,
                '年龄分组_编码': 1, '血压分类_编码': 0, '尿蛋白_编码': 0, '尿糖_编码': 0
            }
        },
        {
            "name": "患者B", 
            "data": {
                '产检孕周': 38, '年龄': 37, '孕次': 2, '产次': 1, '流产史': 0,
                '既往GDM史': 0, '既往病史': 1, '家族史': 0, 'BMI': 28.5, '体重': 75.0,
                '收缩压': 145, '舒张压': 95, '血红蛋白': 105, '白细胞计数': 12.0,
                '血小板计数': 180, '分娩孕周': 0, '产后出血量': 0,
                '就诊类型_编码': 0, '民族_编码': 0, 'BMI分类_编码': 3,
                '年龄分组_编码': 2, '血压分类_编码': 2, '尿蛋白_编码': 1, '尿糖_编码': 0
            }
        }
    ]
    
    # 进行预测
    for patient in demo_patients:
        print(f"\n{patient['name']} 的风险评估:")
        print("-" * 30)
        
        result = predictor.predict_single(patient['data'])
        if "error" not in result:
            print(f"风险等级: {result['risk_label']}")
            print(f"置信度: {result['confidence']:.2%}")
            print("各等级概率:")
            for level, prob in result['probabilities'].items():
                print(f"  {level}: {prob:.2%}")
        else:
            print(f"预测失败: {result['error']}")
    
    # 显示特征重要性
    print(f"\n特征重要性分析:")
    print("-" * 30)
    top_features = predictor.plot_feature_importance()
    if top_features is not None:
        print("前5个最重要的特征:")
        for i, row in top_features.head(5).iterrows():
            print(f"  {row['feature']}: {row['importance']:.4f}")

if __name__ == "__main__":
    demo_prediction()
